"""
飞书文档搜索相关工具。
"""

import os
import uuid
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.utils.logger import logger
from src.services.agent.tools.tool_manager import tool_manager
from src.services.agent.tools.feishu_group_chat_content_tool import search_feishu_group_chat_content

FILE_CONTENT_LIMIT=int(os.getenv("FILE_CONTENT_LIMIT", 2000))

# 飞书文档类型表：https://open.feishu.cn/document/server-docs/docs/wiki-v2/search_wiki
FEISHU_DOC_TYPE_SET=set([1,5,6,7,8]) # 取doc/docx/wiki/slide/file

async def search_feishu_docs(
    wrapper: RunContextWrapper[UserInfo],
    query: str,
    page_size: int = 10,
    get_content: bool = True
) -> <PERSON><PERSON>[List[Dict[str, Any]], str]:
    """搜索飞书文档并获取内容，同时并行搜索群聊消息。

    该工具用于在飞书知识库中搜索相关文档，可以帮助AI获取公司内部的业务知识和规则定义。
    现在会同时搜索飞书文档和群聊消息，为AI提供更丰富的知识来源。
    工作流程：
    1. 并行调用文档搜索和群聊搜索接口
    2. 如果get_content为True，会进一步调用详情接口获取每个文档的具体内容
    3. 合并两个搜索结果，并标识数据来源

    Args:
        wrapper: 包含用户信息的上下文包装器。
        query: 搜索关键词，建议使用具体的业务术语或问题描述。
        page_size: 返回结果数量，默认为10，最大不超过50。
        get_content: 是否获取文档详细内容，默认为True。如果为True，会进一步调用详情接口获取每个文档的具体内容。您可以根据需要来决定是否调用此工具。

    Returns:
        Tuple[List[Dict[str, Any]], str]: 搜索结果列表和描述信息。
        搜索结果包含：title（标题）、url（链接）、obj_token（文档标识）、content（内容，仅当get_content=True时）、source（数据来源：'document'或群聊名称）
    """
    user_info = wrapper.context
    access_token = user_info.access_token

    if not access_token:
        error_msg = "用户未提供飞书访问令牌"
        logger.warning(error_msg)
        return [], f"搜索飞书文档失败: {error_msg}"

    search_id = str(uuid.uuid4())
    logger.info(f"用户开始搜索飞书文档和群聊，搜索ID: {search_id}, 关键词: {query}, access_token: {access_token[:20]}...")

    # 并行调用文档搜索和群聊搜索
    try:
        # 创建并行任务
        doc_search_task = _search_documents_only(wrapper, query, page_size, get_content)
        chat_search_task = _search_group_chats_safe(wrapper, query)

        # 并行执行两个搜索任务
        doc_results, chat_results = await asyncio.gather(
            doc_search_task,
            chat_search_task,
            return_exceptions=True
        )

        # 处理文档搜索结果
        if isinstance(doc_results, Exception):
            logger.exception(f"文档搜索失败: {doc_results}")
            doc_items = []
        else:
            doc_items, _ = doc_results
            # 为文档结果添加来源标识
            for item in doc_items:
                item['source'] = 'document'

        # 处理群聊搜索结果
        if isinstance(chat_results, Exception):
            logger.exception(f"群聊搜索失败: {chat_results}")
            chat_items = []
        else:
            chat_items, _ = chat_results
            # 为群聊结果添加来源标识和统一格式
            for item in chat_items:
                # 使用群聊名称作为数据来源标识
                chat_name = item.get('chat_name', '未知群聊')
                item['source'] = chat_name
                # 统一字段格式，将群聊消息格式转换为类似文档的格式
                if 'content' in item and 'title' not in item:
                    # 从消息内容中提取前50个字符作为标题，并包含群聊名称
                    content_preview = item['content'][:50] + "..." if len(item.get('content', '')) > 50 else item.get('content', '')
                    item['title'] = f"[{chat_name}] {content_preview}"
                if 'message_id' in item and 'obj_token' not in item:
                    item['obj_token'] = item['message_id']  # 使用message_id作为标识符
                if 'url' not in item:
                    item['url'] = f"飞书群聊消息 (群聊: {chat_name})"

        # 合并结果
        all_items = doc_items + chat_items

        # 生成综合描述信息
        total_docs = len(doc_items)
        total_chats = len(chat_items)
        total_items = len(all_items)

        if total_items == 0:
            return [], f"未找到与关键词 '{query}' 相关的文档或群聊消息"

        description_parts = []
        if total_docs > 0:
            description_parts.append(f"{total_docs} 个文档")
        if total_chats > 0:
            description_parts.append(f"{total_chats} 条群聊消息")

        description = f"成功搜索到 {' 和 '.join(description_parts)}，共 {total_items} 条结果"
        if get_content:
            description += "并获取了内容"

        logger.info(f"搜索完成: {description}")
        return all_items, description

    except Exception as e:
        error_msg = f"搜索过程中发生异常: {str(e)}"
        logger.exception(error_msg)
        return [], f"搜索失败: {error_msg}"


async def _search_documents_only(
    wrapper: RunContextWrapper[UserInfo],
    query: str,
    page_size: int = 10,
    get_content: bool = True
) -> Tuple[List[Dict[str, Any]], str]:
    """仅搜索飞书文档的内部函数"""
    user_info = wrapper.context
    access_token = user_info.access_token

    try:
        # 调用搜索接口
        search_url = f"https://open.feishu.cn/open-apis/wiki/v1/nodes/search?page_size={page_size}"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}'
        }
        search_data = {'query': query}

        # 设置超时时间为30秒，避免长时间等待
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(search_url, headers=headers, json=search_data) as response:
                if response.status != 200:
                    error_msg = f"搜索接口调用失败，状态码: {response.status}, 响应内容: {await response.text()}"
                    logger.exception(error_msg)
                    return [], f"搜索飞书文档失败: {error_msg}"

                search_result = await response.json()

                if search_result.get('code') != 0:
                    error_msg = f"搜索接口返回错误: {search_result.get('msg', '未知错误')}"
                    logger.exception(error_msg)
                    return [], f"搜索飞书文档失败: {error_msg}"

                items = search_result.get('data', {}).get('items', [])
                filtered_items = []
                for item in items:
                    logger.info(f"搜索到文档: {item['title']}, 链接: {item['url']}, obj_type: {item['obj_type']}")
                    if item['obj_type'] in FEISHU_DOC_TYPE_SET:
                        filtered_items.append(item)
                    else:
                        logger.info(f"跳过文档: {item['title']}, 链接: {item['url']}, obj_type: {item['obj_type']}")
                items = filtered_items

                if not items:
                    return [], f"未找到与关键词 '{query}' 相关的文档"

                # 如果不需要获取内容，直接返回搜索结果
                if not get_content:
                    return items, f"成功搜索到 {len(items)} 个相关文档"

                # 获取每个文档的详细内容
                enriched_items = []
                for item in items:
                    obj_token = item.get('obj_token')
                    if obj_token:
                        content = await _get_feishu_doc_content(access_token, obj_token, get_all=False)
                        item['content'] = content
                    enriched_items.append(item)

                return enriched_items, f"成功搜索到 {len(enriched_items)} 个相关文档并获取了内容"

    except Exception as e:
        error_msg = f"搜索飞书文档时发生异常: {str(e)}"
        logger.exception(error_msg)
        return [], f"搜索飞书文档失败: {error_msg}"


async def _search_group_chats_safe(
    wrapper: RunContextWrapper[UserInfo],
    query: str,
    page_size: int = 30,
    get_content: bool = True
) -> Tuple[List[Dict[str, Any]], str]:
    """安全地搜索群聊消息的包装函数，确保错误不会影响文档搜索"""
    try:
        logger.info(f"开始并行搜索群聊消息，关键词: {query}")
        return await search_feishu_group_chat_content(wrapper, query, page_size, get_content)
    except Exception as e:
        logger.warning(f"群聊搜索出现异常，但不影响文档搜索: {str(e)}")
        return [], f"群聊搜索失败: {str(e)}"


async def get_feishu_doc_content_tool(
    wrapper: RunContextWrapper[UserInfo], 
    obj_token: str
) -> Tuple[Optional[str], str]:
    """获取指定飞书文档的纯文本内容。

    Args:
        wrapper: 包含用户信息的上下文包装器。
        obj_token: 文档的obj_token标识符。

    Returns:
        Tuple[Optional[str], str]: 文档内容和描述信息。
    """
    user_info = wrapper.context
    access_token = user_info.access_token
    
    if not access_token:
        error_msg = "用户未提供飞书访问令牌"
        logger.warning(error_msg)
        return None, f"获取文档内容失败: {error_msg}"
    
    logger.info(f"开始获取飞书文档内容，obj_token: {obj_token}")
    
    content = await _get_feishu_doc_content(access_token, obj_token, get_all=True)
    
    if content:
        return content, f"成功获取文档内容，长度: {len(content)} 字符"
    else:
        return None, f"获取文档内容失败，obj_token: {obj_token}"


async def _get_feishu_doc_content(access_token: str, obj_token: str, get_all: bool = False) -> Optional[str]:
    """内部函数：获取飞书文档的纯文本内容。
    
    Args:
        access_token: 飞书访问令牌。
        obj_token: 文档的obj_token标识符。
        get_all: 是否获取全部内容，默认为False。

    Returns:
        Optional[str]: 文档的纯文本内容，获取失败时返回None。
    """
    try:
        logger.info(f"正在获取飞书文档markdown内容，obj_token: {obj_token}, access_token: {access_token[:20]}..., get_all: {get_all}")
        content_url = f"https://open.feishu.cn/open-apis/docs/v1/content?content_type=markdown&doc_token={obj_token}&doc_type=docx"
        headers = {
            'Authorization': f'Bearer {access_token}'
        }
        
        # 设置超时时间为30秒，避免长时间等待
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(content_url, headers=headers) as response:
                if response.status != 200:
                    logger.exception(f"获取文档内容失败，状态码: {response.status}, obj_token: {obj_token}, 响应内容: {await response.text()}")
                    return None
                
                content_result = await response.json()
                
                if content_result.get('code') != 0:
                    logger.exception(f"获取文档内容接口返回错误: {content_result.get('msg', '未知错误')}, obj_token: {obj_token}")
                    return None
                
                content = content_result.get('data', {}).get('content', '').strip()
                logger.info(f"获取到文档内容，obj_token: {obj_token}, 内容长度: {len(content)} 字符")
                if get_all:
                    return content
                if len(content) > FILE_CONTENT_LIMIT:
                    content = content[:FILE_CONTENT_LIMIT] + f"\n\n(内容已被截断, 总长度{len(content)}, 截断到{FILE_CONTENT_LIMIT}，如果有需要，请使用get_feishu_doc_content_tool工具获取全部内容)"
                logger.info(f"前50个字符: {content[:50]}") # 输出前50个字符，用于调试
                return content
                
    except Exception as e:
        logger.exception(f"获取文档内容时发生异常: {str(e)}, obj_token: {obj_token}")
        return None


# 注册工具
tool_manager.register_as_function_tool(search_feishu_docs)
tool_manager.register_as_function_tool(get_feishu_doc_content_tool)